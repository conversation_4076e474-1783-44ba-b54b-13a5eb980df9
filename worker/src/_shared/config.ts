import { env } from "node:process";
import { z } from "zod/v4";

const configSchema = z.object({
  GOOGLE_APPLICATION_CREDENTIALS: z.string().optional(),
  NAMESPACE: z.string().default("default"),
  NODE_ENV: z.enum(["development", "production"]).default("production"),
  TASK_QUEUE: z.string().default("task-queue"),
  TEMPORAL_CONNECTION: z.object({
    address: z.string().default("localhost:7233"),
    tls: z.boolean().default(false),
  }),
});

const parsed = configSchema.safeParse({
  GOOGLE_APPLICATION_CREDENTIALS: env.GOOGLE_APPLICATION_CREDENTIALS,
  NODE_ENV: env.NODE_ENV,
  TEMPORAL_CONNECTION: {
    address: env.TEMPORAL_CONNECTION_ADDRESS,
    tls: env.TEMPORAL_CONNECTION_TLS === "true",
  },
});

if (parsed.success) {
  console.info("✅ Valid environment variables");
  console.info("NODE_ENV:", parsed.data.NODE_ENV);
}
else {
  console.error("❌ Invalid environment variables:", parsed.error.toString());
  process.exit(1);
}

export const config = parsed.data;
