import type {
  FastifyInstance,
  FastifyPluginAsync,
  FastifyRequest,
  RouteOptions,
} from "fastify";
import type { UserRecord } from "@/_shared/firebase";

import fp from "fastify-plugin";
import { MyError, StatusCodes } from "@/_shared/error";
import { getAuth } from "@/_shared/firebase";
import { tryCatch } from "@/_shared/tryCatch";

// Define a custom interface for route options with auth
type RouteOptionsWithAuth = {
  auth?: boolean;
} & RouteOptions;

async function validateToken(token: string): Promise<UserRecord> {
  const firebaseAuth = getAuth();
  const decodedToken = await tryCatch(firebaseAuth.verifyIdToken(token, true));
  if (decodedToken.error) {
    throw new MyError(
      "error",
      "validateToken",
      "INVALID_TOKEN",
      StatusCodes.UNAUTHORIZED,
      decodedToken.error,
      {},
    );
  }
  const user = await firebaseAuth.getUser(decodedToken.result.uid);
  return user;
}

function extractToken(authHeader: string | undefined): string {
  if (!authHeader) {
    throw new MyError(
      "error",
      "extractToken",
      "NO_AUTHORIZATION_HEADER",
      StatusCodes.UNAUTHORIZED,
      "No authorization header",
      {},
    );
  }

  if (!authHeader.startsWith("Bearer ")) {
    throw new MyError(
      "error",
      "extractToken",
      "INVALID_AUTHORIZATION_HEADER",
      StatusCodes.UNAUTHORIZED,
      "Invalid authorization header format",
      {},
    );
  }

  const token = authHeader.split(" ")[1];

  if (!token) {
    throw new MyError(
      "error",
      "extractToken",
      "NO_TOKEN_PROVIDED",
      StatusCodes.UNAUTHORIZED,
      "No token provided",
      {},
    );
  }

  return token;
}

async function authenticate(request: FastifyRequest) {
  const token = extractToken(request.headers.authorization);
  const decodedToken = await validateToken(token);
  request.user = decodedToken;
}

export const authPlugin: FastifyPluginAsync = fp(
  async (fastify: FastifyInstance) => {
    // Add auth decorator to all routes by default
    fastify.addHook("onRoute", (routeOptions: RouteOptionsWithAuth) => {
      // Set auth to true by default if not explicitly set
      if (routeOptions.auth === undefined) {
        routeOptions.auth = true;
      }
    });

    // Authenticate the request during prehandler
    fastify.addHook("preHandler", async (req) => {
      const routeConfig = req.routeOptions.config as { auth?: boolean };
      if (routeConfig.auth === false)
        return;

      // Authenticate the request
      await authenticate(req as FastifyRequest);
    });
  },
);
