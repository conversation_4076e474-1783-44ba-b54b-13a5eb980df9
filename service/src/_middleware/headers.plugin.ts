import type { FastifyInstance, FastifyPluginAsync } from "fastify";

import fp from "fastify-plugin";

import { HeadersSchema } from "@/_shared/common.schema";
import { config } from "@/_shared/config";
import { MyError, StatusCodes } from "@/_shared/error";

export const headerValidationPlugin: FastifyPluginAsync = fp(
  async (fastify: FastifyInstance) => {
    // Add a hook to validate headers before handling the request
    fastify.addHook("onRequest", async (request) => {
      // Validate headers on every request
      const parsed = HeadersSchema.safeParse(request.headers);
      if (!parsed.success) {
        throw new MyError(
          "error",
          "headerValidationPlugin",
          "BAD_REQUEST",
          StatusCodes.BAD_REQUEST,
          parsed.error,
          {},
        );
      }
      if (
        parsed.data
        && parsed.data["x-app-version"] < config.minAppVersion
      ) {
        throw new MyError(
          "error",
          "headerValidationPlugin",
          "UPGRADE_REQUIRED",
          StatusCodes.UPGRADE_REQUIRED,
          "Please upgrade the app",
          {
            "x-app-version": parsed.data["x-app-version"],
          },
        );
      }
      // Set the parsed headers back to the request object
      request.platformName = parsed.data["x-platform-name"];
      request.traceId = parsed.data["x-trace-id"];
      request.timeZoneName = parsed.data["x-tz-name"];
      request.timeZoneOffset = parsed.data["x-tz-offset"];
    });
  },
);
