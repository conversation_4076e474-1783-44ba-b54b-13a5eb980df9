/* eslint-disable node/no-process-env */
import { ConfigSchema } from "@/_shared/config.schema";
import { z } from "zod/v4";

const parsed = ConfigSchema.safeParse({
  env: process.env.NODE_ENV,
  googleApplicationCredentials: process.env.GOOGLE_APPLICATION_CREDENTIALS,
  host: process.env.HOST,
  isEnvDevelopment: process.env.NODE_ENV === "development",
  isEnvTest: process.env.NODE_ENV === "test",
  logLevel: process.env.LOG_LEVEL || "warn",
  minAppVersion:
    process.env.MINIMUM_APP_VERSION
      ? Number.parseInt(process.env.MINIMUM_APP_VERSION, 10)
      : 1,
  port: process.env.PORT ? Number.parseInt(process.env.PORT, 10) : undefined,
  useEmulator: process.env.USE_EMULATOR === "true",
});

if (!parsed.success) {
  console.error("❌ Invalid environment variables:", z.prettifyError(parsed.error));
  process.exit(1);
}

export const config = parsed.data;
