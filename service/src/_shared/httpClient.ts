import type { AxiosResponse } from "axios";

import axios from "axios";

import { MyError, StatusCodes } from "@/_shared/error";
import { tryCatch } from "@/_shared/tryCatch";
import { ZodObject } from "zod/v4";

export type HttpClientResponse = {
  status: StatusCodes;
  headers: Record<string, string>;
  data: Record<string, unknown>;
};

export class HttpClient {
  private static isRequestSuccessful(response: AxiosResponse): boolean {
    return response.status >= 200 && response.status < 300;
  }

  static async get(
    url: string,
    type: ZodObject,
    params: Record<string, unknown> = {},
    headers: Record<string, string> = {},
  ): Promise<HttpClientResponse> {
    // Add default headers if not provided
    headers["Content-Type"] = headers["Content-Type"] || "application/json";
    headers.Accept = headers.Accept || "application/json";
    // Make the HTTP GET request
    const response = await tryCatch(axios.get(url, { headers, params }));
    if (response.error) {
      throw new MyError(
        "error",
        "HttpClient.get",
        "HTTP_ERROR",
        StatusCodes.INTERNAL_SERVER_ERROR,
        response.error,
        { headers, params, url },
      );
    }
    if (!this.isRequestSuccessful(response.result)) {
      throw new MyError(
        "error",
        "HttpClient.get",
        "HTTP_ERROR",
        StatusCodes.INTERNAL_SERVER_ERROR,
        new Error(`HTTP error! status: ${response.result.status}`),
        {
          data: response.result.data,
          headers,
          params,
          status: response.result.status,
          url,
        },
      );
    }
    const parsedData = type.safeParse(response.result.data);
    if (!parsedData.success) {
      throw new MyError(
        "error",
        "HttpClient.get",
        "INVALID_RESPONSE",
        StatusCodes.INTERNAL_SERVER_ERROR,
        parsedData.error,
        { data: response.result.data, headers, params, url },
      );
    }
    return {
      data: parsedData.data as Record<string, unknown>,
      headers: response.result.headers as Record<string, string>,
      status: response.result.status as StatusCodes,
    };
  }
}
