import type { FastifyInstance } from "fastify";

import type { PlatformName, PlatformType } from "@/_shared/common.schema";
import cors from "@fastify/cors";
import helmet from "@fastify/helmet";
import type { UserRecord } from "firebase-admin/auth";

import { authPlugin } from "@/_middleware/auth.plugin";
import { errorHandler } from "@/_middleware/error.handler";
import { headerValidationPlugin } from "@/_middleware/headers.plugin";
import { config } from "@/_shared/config";
import { loggerConfig } from "@/_shared/logger";
import { setupRoutes } from "@/modules/routes";
import Fastify from "fastify";
import {
  serializerCompiler,
  validatorCompiler,
} from "fastify-type-provider-zod";

export type { FastifyInstance };

// Extend Fastify types
declare module "fastify" {
  // eslint-disable-next-line ts/consistent-type-definitions
  interface FastifyRequest {
    user?: UserRecord;
    platformName?: PlatformName;
    platformType?: PlatformType;
    traceId?: string;
    timeZoneName?: string;
    timeZoneOffset?: number;
  }

  // eslint-disable-next-line ts/consistent-type-definitions
  interface RouteShorthandOptions {
    auth?: boolean;
  }
}

export async function buildApp(): Promise<FastifyInstance> {
  const app = Fastify({
    bodyLimit: 10_240, // 10KB
    disableRequestLogging: !config.isEnvDevelopment,
    // logger: config.isEnvTest ? false : loggerConfig,
    logger: loggerConfig,
    requestTimeout: 30_000, // 30 seconds
  });

  // Register error handler
  app.setErrorHandler(errorHandler);

  // Register plugins
  await app.register(cors, {
    credentials: true,
    methods: ["GET", "POST"],
    origin: (origin, cb) => {
      // Allow requests with no origin (like mobile apps)
      if (!origin) {
        return cb(null, true);
      }

      // Check if the origin is in our allowed list
      const allowedOrigins = ["https://95octane.app", "https://95octane.dev"];
      if (allowedOrigins.includes(origin)) {
        return cb(null, true);
      }

      // Otherwise, deny the request
      return cb(new Error("Not allowed by CORS"), false);
    },
  });

  await app.register(helmet, {
    global: true,
  });

  // Register type provider
  app.setSerializerCompiler(serializerCompiler);
  app.setValidatorCompiler(validatorCompiler);

  // Register Auth plugin
  await app.register(authPlugin);

  // Register Headers plugin
  await app.register(headerValidationPlugin);

  // Register routes
  setupRoutes(app);

  return app;
}

export async function stopServer(app: FastifyInstance): Promise<void> {
  console.info("Stopping server...");
  await app.close();
  console.info("Server stopped successfully.");
  process.exit(0);
}
