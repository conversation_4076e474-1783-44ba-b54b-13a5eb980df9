FROM chainguard/node:latest AS base

ARG RELEASE_TYPE
USER root
WORKDIR /app
ENV PNPM_HOME=/app/pnpm
ENV PATH="${PNPM_HOME}:${PATH}"

RUN npm install --global pnpm@latest

COPY package.json pnpm-lock.yaml* pnpm-workspace.yaml* ./
COPY service/package.json ./service/

FROM base AS build-deps

RUN pnpm install --frozen-lockfile

COPY ./service ./service/
RUN cd service && pnpm run build:${RELEASE_TYPE}

FROM base AS prod-deps
# Install only production dependencies in a clean stage
RUN pnpm install --prod --frozen-lockfile

FROM chainguard/node:latest AS target
ARG RELEASE_TYPE
USER root
WORKDIR /app

# Copy only the production node_modules from the prod-deps stage
COPY --from=prod-deps /app/node_modules ./node_modules
COPY --from=prod-deps /app/service/node_modules ./service/node_modules

# Copy the build artifacts
COPY --from=build-deps /app/service/dist ./service/dist

# Copy package files (needed for runtime)
COPY package.json pnpm-lock.yaml* pnpm-workspace.yaml* ./
COPY service/package.json ./service/

USER node

ENV HOST=0.0.0.0
ENV PORT=8888
ENV NODE_ENV=${RELEASE_TYPE}

EXPOSE 8888
CMD ["--enable-source-maps", "./service/dist/app.js"]