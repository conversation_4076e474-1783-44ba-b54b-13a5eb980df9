[group("extras")]
[doc('Count lines of code')]
@count:
	echo -en "{{GREEN + BOLD}}Counting lines of code ... {{NORMAL}}"
	find . -name "*.ts" -not -path "*/node_modules/*" -not -path "*/dist/*" -not -path "*/.git/*" -not -path "*/build/*" -not -path "*/coverage/*" -exec cat {} \; | wc -l | awk '{print $1}' | numfmt --grouping

[group('common')]
[doc('Setup the project: install precommit hooks, upgrade dependencies, clean up cache, etc.')]
@setup: install _install-global-packages update-deps setup-precommit run-precommit

[group('common')]
[doc('Install dependencies on each target')]
@_setup-each target: (pnpm-outdated target)

@_cleanup: (_common-clean ".") (_common-clean "service") (_common-clean "worker") (_common-clean "tasks")

[group('common')]
[doc('Install dependencies')]
@install: _cleanup
  echo -e "{{GREEN + BOLD}}Updating package.json ...{{NORMAL}}"
  npm list --global | grep pnpm | sed 's/.*pnpm@\([0-9.]*\).*/\1/' | xargs -I {} jq '.packageManager = "pnpm@{}"' ./package.json > tmp.json && mv tmp.json package.json
  echo -e "{{GREEN + BOLD}}Installing node packages ...{{NORMAL}}"
  pnpm install --color --aggregate-output
  echo -e "{{GREEN + BOLD}}Check for outdated packages ...{{NORMAL}}"
  pnpm outdated --color --aggregate-output --recursive --long --depth 0

[group('common')]
[doc('Run pnpm audit')]
@pnpm-audit target:
  echo -e "{{GREEN + BOLD}}Run pnpm audit on packages ({{target}})...{{NORMAL}}"
  cd {{target}} && pnpm audit

[group('common')]
[doc('Run pnpm outdated')]
@pnpm-outdated target:
  echo -e "{{GREEN + BOLD}}Run pnpm outdated on packages ({{target}})...{{NORMAL}}"
  -cd {{target}} && pnpm outdated

[group('common')]
[doc('Run pre-commit hooks on all files')]
@run-precommit:
  echo -e "{{GREEN + BOLD}}Running pre-commit ...{{NORMAL}}"
  pre-commit run --all-files

[group('common')]
[doc('Update pre-commit hooks')]
@setup-precommit:
  echo -e "{{GREEN + BOLD}}Updating pre-commit hooks ...{{NORMAL}}"
  pre-commit autoupdate
  echo -e "{{GREEN + BOLD}}Installing commit-msg hook ...{{NORMAL}}"
  pre-commit install --hook-type commit-msg
  echo -e "{{GREEN + BOLD}}Please upgrade pre-commit using homebrew: brew upgrade pre-commit{{NORMAL}}"

[group('common')]
[doc('Install firebase-tools')]
@_install-global-packages:
  echo -e "{{GREEN + BOLD}}Install global dependencies using npm ...{{NORMAL}}"
  npm install --global pnpm firebase-tools

[group('common')]
[doc('Clean up the project: delete dist and node_modules')]
@_common-clean target:
  echo -e "{{GREEN + BOLD}}Deleting {{target}}/dist/* ...{{NORMAL}}"
  -rm -rf {{target}}/dist
  echo -e "{{GREEN + BOLD}}Deleting node_modules ({{target}}) ...{{NORMAL}}"
  -cd {{target}} && rm -rf package-lock.json yarn.lock pnpm-lock.yaml node_modules

[group('common')]
[doc('Update dependency images')]
@update-deps: stop-deps _pull-deps start-deps _cleanup-deps

[group('common')]
[doc('Start the dependencies using compose')]
@start-deps:
  echo -e "{{GREEN + BOLD}}Starting the dependencies ...{{NORMAL}}"
  docker compose --file ./utils/docker-compose.yaml up --detach
  just import-local

[group('common')]
[doc('Start the dependencies using compose')]
@stop-deps:
  echo -e "{{GREEN + BOLD}}Stopping the dependencies ...{{NORMAL}}"
  docker compose --file ./utils/docker-compose.yaml down --remove-orphans --volumes

@_pull-deps:
  echo -e "{{GREEN + BOLD}}Pulling the dependencies ...{{NORMAL}}"
  docker compose --file ./utils/docker-compose.yaml pull --policy always

@_cleanup-deps:
  echo -e "{{GREEN + BOLD}}Cleaning up the dependencies ...{{NORMAL}}"
  docker system prune --volumes --force

[group('common')]
[doc('Run gitleaks')]
@leaks:
  echo -e "{{GREEN + BOLD}}Run gitleaks ...{{NORMAL}}"
  gitleaks dir . --verbose
