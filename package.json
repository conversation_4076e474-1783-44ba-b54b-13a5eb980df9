{"name": "@95octane/backend", "private": true, "scripts": {"build": "pnpm run build:parallel", "build:dev": "concurrently \"cd service && pnpm run build:dev\" \"cd worker && pnpm run build:dev\"", "build:parallel": "concurrently \"cd service && pnpm run build:prod\" \"cd worker && pnpm run build:prod\"", "build:perf": "node scripts/build-performance.mjs", "clean": "concurrently \"cd service && pnpm run clean\" \"cd worker && pnpm run clean\"", "clean:cache": "concurrently \"cd service && pnpm run clean:cache\" \"cd worker && pnpm run clean:cache\"", "lint": "eslint --color --ext .json,.ts,.js .", "lint:fix": "eslint --fix --color --ext .json,.ts,.js .", "serve": "concurrently \"pnpm run service\" \"pnpm run worker\"", "service": "cd service && pnpm run dev", "worker": "cd worker && pnpm run dev"}, "devDependencies": {"@antfu/eslint-config": "latest", "concurrently": "latest", "eslint": "latest", "eslint-plugin-format": "latest"}, "packageManager": "pnpm@10.11.1"}