# Coding Standards

## Notes

- This repo is a mono-repo with multiple projects.
- `Service` project is an ESM module.
- `Worker` project is a Temporal worker and also an ESM module.
- `Test` project is a Jest project to test the `Service` project and is a CommonJS module.
- `Docs` project is a collection of markdown files to document the project.

## Naming Conventions

- Use descriptive, meaningful names that convey purpose
- Use camelCase for variable and function names
- Variables with auxiliary verbs (isLoading, hasError)
- Use PascalCase for class names, interfaces, and types
- Use lowercase with hyphens for directories (kebab-case)
- Use singular nouns for module names (user, ride, route)
- Use `.ts` extension for all TypeScript files
- Name files based on their module and responsibility (user.service.ts, user.routes.ts)

## Imports

- Group imports in the following order:
  1. Node.js built-in modules
  2. External dependencies
  3. Internal modules (using absolute paths with @ alias)
- Sort imports alphabetically within each group
- Use destructured imports where appropriate
- Use `@/` prefix for imports

```typescript
// Internal modules
import { MyError } from "@/_shared/error";
import { successResponse } from "@/modules/common/common.schema";
import { userSchema } from "@/modules/user/user.schema";

// External dependencies
import { FastifyInstance } from "fastify";
import { ZodTypeProvider } from "fastify-type-provider-zod";
import { z } from "zod/v4";
```

## TypeScript Usage

- Use TypeScript for all code
- Use strict mode with accurate types
- Use Zod schemas for object shapes
- Prefer explicit typing over inferred types for function parameters and return values
- Use named functions with explicit return types
- Export types and interfaces for reuse.
- Use Zod for schema validation and type inference
- Use type assertions sparingly and only when necessary
- Use Zod schemas for API routes, parameters, requests, and responses

```typescript
// Define schema with Zod
export const userResponseSchema = z.object({
  id: z.string().min(10),
  name: z.string().nullable(),
  // ...other properties
});

// Infer TypeScript type from schema
export type UserResponse = z.infer<typeof userResponseSchema>;

// Use explicit typing for function parameters and return values
export async function getUser(req: FastifyRequest): Promise<UserResponse> {
  // Implementation
}
```

## Fastify Implementation

### Route Definition

- Use POST method for API endpoints
- Group routes by domain in separate modules
- Use Zod for request/response validation
- Define schemas for parameters, body, and response

```typescript
export function setupUserRoutes(app: FastifyInstance) {
  app.withTypeProvider<ZodTypeProvider>().post("/user/:userId", {
    schema: {
      params: userIdParam,
      body: userRequestSchema,
      response: {
        200: userResponseSchema,
      },
    },
    handler: getUser,
  });
}
```

### API Handlers

- Keep API handlers focused on request/response handling
- Delegate business logic to service layer
- Use proper error handling
- Return typed responses

```typescript
export async function getUser(req: FastifyRequest): Promise<UserResponse> {
  const { userId } = req.params as { userId: string };
  // Validation and business logic
  const user = await UserService.get(inputUser.data);
  return { user, config, locationGrid };
}
```

## Firebase Implementation

- Use Firebase Admin SDK for server-side operations
- Create service wrappers around Firebase APIs
- Use static methods for Firebase service access
- Handle Firebase errors properly

```typescript
export class FirebaseService {
  private static initialized: boolean = false;
  private static firestore: admin.firestore.Firestore;

  private static initialize() {
    // Initialization logic
  }

  public static getAuth() {
    this.initialize();
    return admin.auth();
  }
}
```

## Error Handling

- Use custom error classes (`MyError`) for errors
- Include error codes and HTTP status codes
- Use try/catch blocks for async operations
- Log errors with appropriate level

```typescript
class AuthenticationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "AuthenticationError";
  }
}

try {
  const result = await someAsyncOperation();
}
catch (error) {
  console.error(`Operation failed: ${JSON.stringify(error)}`);
  throw new MyError("Operation failed", 500, "OPERATION_FAILED");
}
```

## Asynchronous Operations

- Use async/await for asynchronous operations
- Avoid nested promises and callbacks
- Handle Promise rejections properly

```typescript
async function startServer() {
  const app = await buildApp();
  try {
    await app.listen({ port: config.PORT, host: config.HOST });
  }
  catch (err) {
    console.error(`Server failed to start: ${JSON.stringify(err)}`);
    process.exit(1);
  }
}
```

## Configuration

- Use environment variables for configuration
- Provide sensible defaults
- Validate configuration at startup

```typescript
export const config = {
  PORT: process.env.PORT ? Number.parseInt(process.env.PORT, 10) : 8080,
  HOST: process.env.HOST || "0.0.0.0",
  NODE_ENV: process.env.NODE_ENV || "development",
};

export const isDevelopment = config.NODE_ENV === "development";
```

## Logging

- Use Fastify's built-in logger in API Service
- Use Logger class in Worker which will be using Pino
- Configure different log levels for different environments
- Include context in log messages
- Log all errors and important events

## Testing

- Use Jest for testing
- Write unit tests for business logic
- Mock external dependencies
- Use integration tests for API endpoints
- Use consistent testing patterns and naming conventions

## Code Formatting

- Use Prettier for consistent formatting. Follow prettier configuration file `.prettierrc` from the root of the project.
- Use ESLint for linting. Follow eslint configuration file `eslint.config.mjs` from the root of the project.
- Use consistent indentation (2 spaces)
- Use semicolons at the end of statements
- Use single quotes for strings
- Limit line length to 120 characters
